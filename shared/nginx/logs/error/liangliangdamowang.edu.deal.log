2025/07/26 20:39:14 [error] 20#20: *6 directory index of "/var/www/html/" is forbidden, client: **********, server: liangliangdamowang.edu.deal, request: "HEAD / HTTP/2.0", host: "localhost"
2025/07/26 20:55:16 [error] 20#20: *61 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /.env HTTP/1.1", host: "************"
2025/07/26 20:55:17 [error] 20#20: *62 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "POST / HTTP/1.1", host: "************"
2025/07/26 21:02:55 [error] 285#285: *81 directory index of "/var/www/html/" is forbidden, client: *************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://**************/"
2025/07/26 21:13:21 [error] 512#512: *99 directory index of "/var/www/html/" is forbidden, client: ***************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://**************:80/"
2025/07/26 21:21:20 [error] 512#512: *137 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /.env HTTP/1.1", host: "************"
2025/07/26 21:39:29 [error] 512#512: *209 directory index of "/var/www/html/" is forbidden, client: *************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/26 22:14:37 [error] 512#512: *349 directory index of "/var/www/html/" is forbidden, client: ***************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://***********/"
2025/07/26 22:15:23 [error] 512#512: *350 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://**************/"
2025/07/26 22:16:52 [error] 512#512: *351 directory index of "/var/www/html/" is forbidden, client: *************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://**************:80"
2025/07/26 22:24:41 [error] 512#512: *352 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://***********:80"
2025/07/26 22:25:02 [error] 512#512: *354 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://************:80"
2025/07/26 22:37:02 [error] 512#512: *361 directory index of "/var/www/html/" is forbidden, client: *************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/26 22:42:36 [error] 512#512: *389 directory index of "/var/www/html/" is forbidden, client: ************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://***********/"
2025/07/26 23:07:30 [error] 512#512: *410 access forbidden by rule, client: ************, server: liangliangdamowang.edu.deal, request: "GET /.env HTTP/1.1", host: "************"
2025/07/26 23:29:26 [error] 20#20: *19 access forbidden by rule, client: ************, server: liangliangdamowang.edu.deal, request: "GET /.env HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/26 23:39:45 [error] 20#20: *80 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://liangliangdamowang.edu.deal"
2025/07/26 23:45:15 [error] 20#20: *86 directory index of "/var/www/html/" is forbidden, client: *************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://**************"
2025/07/26 23:46:14 [error] 20#20: *87 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /.env HTTP/1.1", host: "************"
2025/07/26 23:55:03 [error] 20#20: *92 directory index of "/var/www/html/" is forbidden, client: ************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 00:02:20 [error] 20#20: *142 connect() failed (113: Host is unreachable) while connecting to upstream, client: ***************, server: liangliangdamowang.edu.deal, request: "PUT /ai/api/channel/ HTTP/2.0", upstream: "http://**********:3000/api/channel/", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console/channel"
2025/07/27 00:02:20 [error] 20#20: *142 connect() failed (113: Host is unreachable) while connecting to upstream, client: ***************, server: liangliangdamowang.edu.deal, request: "PUT /ai/api/channel/ HTTP/2.0", upstream: "http://**********:3000/api/channel/", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console/channel"
2025/07/27 00:02:23 [error] 20#20: *142 connect() failed (113: Host is unreachable) while connecting to upstream, client: ***************, server: liangliangdamowang.edu.deal, request: "GET /ai/console/channel HTTP/2.0", upstream: "http://**********:3000/console/channel", host: "liangliangdamowang.edu.deal"
2025/07/27 00:02:27 [error] 20#20: *142 connect() failed (113: Host is unreachable) while connecting to upstream, client: ***************, server: liangliangdamowang.edu.deal, request: "GET /ai/ HTTP/2.0", upstream: "http://**********:3000/", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console/channel"
2025/07/27 00:02:52 [error] 20#20: *149 directory index of "/var/www/html/" is forbidden, client: ***********, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "************"
2025/07/27 00:03:37 [error] 20#20: *151 connect() failed (113: Host is unreachable) while connecting to upstream, client: **************, server: liangliangdamowang.edu.deal, request: "GET /ai/console/channel HTTP/2.0", upstream: "http://**********:3000/console/channel", host: "liangliangdamowang.edu.deal"
2025/07/27 00:03:40 [error] 20#20: *151 connect() failed (113: Host is unreachable) while connecting to upstream, client: **************, server: liangliangdamowang.edu.deal, request: "GET /ai/ HTTP/2.0", upstream: "http://**********:3000/", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console/channel"
2025/07/27 00:03:46 [error] 20#20: *151 connect() failed (113: Host is unreachable) while connecting to upstream, client: **************, server: liangliangdamowang.edu.deal, request: "GET /ai/ HTTP/2.0", upstream: "http://**********:3000/", host: "liangliangdamowang.edu.deal"
2025/07/27 00:03:49 [error] 20#20: *151 connect() failed (113: Host is unreachable) while connecting to upstream, client: **************, server: liangliangdamowang.edu.deal, request: "GET /ai/ HTTP/2.0", upstream: "http://**********:3000/", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/"
2025/07/27 00:04:39 [error] 20#20: *157 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /.env HTTP/1.1", host: "************"
2025/07/27 00:04:39 [error] 20#20: *158 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "POST / HTTP/1.1", host: "************"
2025/07/27 00:14:25 [error] 20#20: *282 limiting connections by zone "conn_limit_per_ip", client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/27 00:14:25 [error] 20#20: *282 limiting connections by zone "conn_limit_per_ip", client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/27 00:14:25 [error] 20#20: *282 limiting connections by zone "conn_limit_per_ip", client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/27 00:14:25 [error] 20#20: *282 limiting requests, excess: 10.350 by zone "api", client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/27 00:14:25 [error] 20#20: *282 limiting requests, excess: 10.195 by zone "api", client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/27 00:14:25 [error] 20#20: *282 limiting requests, excess: 10.060 by zone "api", client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/27 00:14:25 [error] 20#20: *282 limiting connections by zone "conn_limit_per_ip", client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/27 00:14:25 [error] 20#20: *282 limiting requests, excess: 10.825 by zone "api", client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/27 00:14:26 [error] 20#20: *282 limiting requests, excess: 10.740 by zone "api", client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/27 00:14:26 [error] 20#20: *282 limiting requests, excess: 10.640 by zone "api", client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/27 00:14:26 [error] 20#20: *282 limiting requests, excess: 10.565 by zone "api", client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/27 00:14:26 [error] 20#20: *282 limiting requests, excess: 10.510 by zone "api", client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/27 01:10:58 [error] 20#20: *404 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 01:12:32 [error] 20#20: *413 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 01:12:33 [error] 20#20: *414 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 01:12:34 [error] 20#20: *415 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /.env HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 01:12:35 [error] 20#20: *416 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /.env.txt HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 01:12:36 [error] 20#20: *417 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /.env.example HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 01:12:37 [error] 20#20: *418 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /.env.old HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 01:12:40 [error] 20#20: *420 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /.aws/credentials HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 01:12:51 [error] 20#20: *429 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /s3cmd.ini HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 01:12:52 [error] 20#20: *430 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /wp-config.php.bak HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 01:12:53 [error] 20#20: *431 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /settings.php.bak HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 01:12:54 [error] 20#20: *432 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /configs/application.ini HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 01:13:00 [error] 20#20: *437 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /.env.bak HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 01:13:05 [error] 20#20: *442 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /.env.dev.local HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 01:13:10 [error] 20#20: *446 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /.git/config HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 01:13:12 [error] 20#20: *448 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /.config HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 01:13:19 [error] 20#20: *454 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /.circleci/config.yml HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 02:02:25 [error] 20#20: *507 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 02:33:23 [error] 20#20: *517 directory index of "/var/www/html/" is forbidden, client: *************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 02:52:46 [error] 19#19: *43 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://***********"
2025/07/27 03:17:05 [error] 19#19: *140 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /.env HTTP/1.1", host: "************"
2025/07/27 03:17:06 [error] 19#19: *141 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "POST / HTTP/1.1", host: "************"
2025/07/27 03:17:39 [error] 19#19: *142 connect() failed (113: Host is unreachable) while connecting to upstream, client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", upstream: "http://**********:3000/v1/chat/completions", host: "liangliangdamowang.edu.deal"
2025/07/27 03:17:43 [error] 19#19: *142 connect() failed (113: Host is unreachable) while connecting to upstream, client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", upstream: "http://**********:3000/v1/chat/completions", host: "liangliangdamowang.edu.deal"
2025/07/27 03:17:50 [error] 19#19: *142 connect() failed (113: Host is unreachable) while connecting to upstream, client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", upstream: "http://**********:3000/v1/chat/completions", host: "liangliangdamowang.edu.deal"
2025/07/27 03:17:59 [error] 19#19: *142 connect() failed (113: Host is unreachable) while connecting to upstream, client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", upstream: "http://**********:3000/v1/chat/completions", host: "liangliangdamowang.edu.deal"
2025/07/27 03:18:02 [error] 19#19: *142 connect() failed (113: Host is unreachable) while connecting to upstream, client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", upstream: "http://**********:3000/v1/chat/completions", host: "liangliangdamowang.edu.deal"
2025/07/27 03:18:28 [error] 19#19: *154 directory index of "/var/www/html/" is forbidden, client: ***************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://***********"
2025/07/27 03:32:11 [error] 19#19: *233 directory index of "/var/www/html/" is forbidden, client: *************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 03:32:11 [error] 19#19: *233 directory index of "/var/www/html/" is forbidden, client: *************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 04:03:50 [error] 19#19: *329 directory index of "/var/www/html/" is forbidden, client: *************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://************:80/"
2025/07/27 04:22:09 [error] 19#19: *349 directory index of "/var/www/html/" is forbidden, client: *************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "************"
2025/07/27 05:06:41 [error] 19#19: *360 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "************"
2025/07/27 05:20:04 [error] 19#19: *378 access forbidden by rule, client: ************, server: liangliangdamowang.edu.deal, request: "GET /.git/config HTTP/1.1", host: "************"
2025/07/27 05:20:05 [error] 19#19: *379 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "************"
2025/07/27 05:27:24 [error] 19#19: *385 directory index of "/var/www/html/" is forbidden, client: ************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 05:57:34 [error] 19#19: *391 access forbidden by rule, client: ************, server: liangliangdamowang.edu.deal, request: "GET /.git/config HTTP/1.1", host: "************"
2025/07/27 05:59:48 [error] 19#19: *392 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://**************/"
2025/07/27 06:33:42 [error] 19#19: *397 access forbidden by rule, client: ************, server: liangliangdamowang.edu.deal, request: "GET /.git/config HTTP/1.1", host: "************"
2025/07/27 06:38:49 [error] 19#19: *399 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://**************:80"
2025/07/27 06:54:30 [error] 19#19: *402 directory index of "/var/www/html/" is forbidden, client: ***************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "************"
2025/07/27 07:14:36 [error] 19#19: *405 directory index of "/var/www/html/" is forbidden, client: *************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "************"
2025/07/27 07:29:36 [error] 19#19: *412 directory index of "/var/www/html/" is forbidden, client: ***************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://**************:80/"
2025/07/27 07:38:47 [error] 19#19: *413 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://***********:80"
2025/07/27 07:39:22 [error] 19#19: *414 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "************"
2025/07/27 07:40:40 [error] 19#19: *423 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "************:443"
2025/07/27 07:54:31 [error] 19#19: *427 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /.env HTTP/1.1", host: "************"
2025/07/27 07:54:32 [error] 19#19: *428 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "POST / HTTP/1.1", host: "************"
2025/07/27 08:01:30 [error] 19#19: *430 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://************:80"
2025/07/27 08:37:34 [error] 19#19: *434 directory index of "/var/www/html/" is forbidden, client: *************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://liangliangdamowang.edu.deal"
2025/07/27 08:45:53 [error] 19#19: *436 access forbidden by rule, client: ***************, server: liangliangdamowang.edu.deal, request: "GET /.env HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 09:08:44 [error] 19#19: *444 directory index of "/var/www/html/" is forbidden, client: *************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 09:09:05 [error] 19#19: *445 directory index of "/var/www/html/" is forbidden, client: *************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 09:23:58 [error] 19#19: *448 directory index of "/var/www/html/" is forbidden, client: ************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://**************"
2025/07/27 09:30:10 [error] 19#19: *450 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "************"
2025/07/27 09:43:16 [error] 19#19: *452 directory index of "/var/www/html/" is forbidden, client: *************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 09:43:17 [error] 19#19: *453 directory index of "/var/www/html/" is forbidden, client: *************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "www.liangliangdamowang.edu.deal"
2025/07/27 09:44:59 [error] 19#19: *454 directory index of "/var/www/html/" is forbidden, client: ***********, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "************"
2025/07/27 09:54:15 [error] 19#19: *468 directory index of "/var/www/html/" is forbidden, client: ***********, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "************"
2025/07/27 10:07:38 [error] 19#19: *473 access forbidden by rule, client: ***********, server: liangliangdamowang.edu.deal, request: "GET /.git/config HTTP/1.1", host: "************"
2025/07/27 10:12:47 [error] 19#19: *475 directory index of "/var/www/html/" is forbidden, client: ***************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/27 10:21:02 [error] 19#19: *481 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://**************:80/"
2025/07/27 10:40:51 [error] 19#19: *483 directory index of "/var/www/html/" is forbidden, client: *************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 11:02:07 [error] 19#19: *501 directory index of "/var/www/html/" is forbidden, client: *************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "www.liangliangdamowang.edu.deal"
2025/07/27 12:03:04 [error] 19#19: *521 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /.env HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:06 [error] 19#19: *522 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /releases/.env HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:07 [error] 19#19: *523 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /test/.env.aws HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:09 [error] 19#19: *524 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /upload/.env.1 HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:10 [error] 19#19: *525 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /shopware/.env HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:11 [error] 19#19: *526 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /test/.env.old HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:13 [error] 19#19: *527 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /test/.env.crt HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:15 [error] 19#19: *529 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /test/.env.bak HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:18 [error] 19#19: *531 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /statisch/.env HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:20 [error] 19#19: *532 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /src/.env.dist HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:22 [error] 19#19: *533 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /stg/.env.dist HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:23 [error] 19#19: *534 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /server/.env.1 HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:24 [error] 19#19: *535 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /server/.env.2 HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:27 [error] 19#19: *537 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /qa/.env.local HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:28 [error] 19#19: *538 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /previous/.env HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:30 [error] 19#19: *539 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /tmp/.env.prod HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:31 [error] 19#19: *540 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /src/.env.test HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:32 [error] 19#19: *541 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /test/.env.log HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:34 [error] 19#19: *542 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /user/.env.bak HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:35 [error] 19#19: *543 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /user/.env.dev HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:37 [error] 19#19: *544 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /demo/.env.bak HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:38 [error] 19#19: *545 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /demo/.env.dev HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:40 [error] 19#19: *546 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /internal/.env HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:44 [error] 19#19: *549 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /lara/.env.bak HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:45 [error] 19#19: *550 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /lara/.env.dev HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:03:47 [error] 19#19: *551 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /live/.env.bak HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:08:10 [error] 19#19: *555 directory index of "/var/www/html/" is forbidden, client: ***********, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://************/"
2025/07/27 12:28:06 [error] 19#19: *561 directory index of "/var/www/html/" is forbidden, client: *************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 12:40:18 [error] 19#19: *571 directory index of "/var/www/html/" is forbidden, client: ************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://***********"
2025/07/27 12:59:44 [error] 19#19: *606 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "************:443"
2025/07/27 13:24:45 [error] 19#19: *668 directory index of "/var/www/html/" is forbidden, client: ************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://***********/"
