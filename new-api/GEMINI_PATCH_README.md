# Gemini API 端点修复补丁系统（简化版）

## 问题描述

在 New-API 项目中，Google Gemini 渠道存在 API 端点错误问题：

### API 端点错误
- **错误端点**: `https://generativelanguage.googleapis.com/v1beta/openai/models`
- **正确端点**: `https://generativelanguage.googleapis.com/v1beta/models`

这个问题导致在渠道管理页面添加 Google Gemini 渠道后，无法正确获取模型列表。

## 解决方案

我们创建了一个简化的补丁系统来解决端点错误问题，确保每次从官方仓库更新代码后都能自动应用修复。

## 修复内容

### 核心修复

#### API 端点修复
- **文件**: `controller/channel.go`
- **修改**: 将 `v1beta/openai/models` 替换为 `v1beta/models`
- **影响位置**:
  - 第182行左右：`FetchUpstreamModels` 函数
  - 第778行左右：`FetchModels` 函数

### 补丁系统组件

#### 专用补丁脚本
- **文件**: `patches/gemini-api-fix.patch`
- **版本**: v2.0-simplified (简化版本)
- **功能**: 仅处理 Gemini API 端点修复
- **命令**:
  ```bash
  ./patches/gemini-api-fix.patch check    # 检查是否需要修复
  ./patches/gemini-api-fix.patch apply    # 应用修复补丁
  ./patches/gemini-api-fix.patch verify   # 验证修复结果
  ./patches/gemini-api-fix.patch details  # 显示修复详情
  ```

#### 集成到更新脚本
- **文件**: `sh/update.sh`
- **功能**:
  - `api-patch` 命令：应用API端点补丁
  - 在完整更新流程中自动应用API补丁
  - 验证功能包含Gemini端点检查

#### 管理脚本集成
- **文件**: `manage-new-api.sh`
- **菜单项**:
  - 代码管理菜单 -> 5) Gemini端点修复

#### 文档更新
- **错误指南**: `new-api错误指南.md` - 包含Gemini API端点错误的详细说明
- **更新指导**: `new-api更新指导.md` - 包含Gemini补丁的创建和应用方法

## 使用方法

### 方法1: 使用管理脚本（推荐）
```bash
./manage-new-api.sh
# 选择: 3) 代码管理 -> 5) Gemini端点修复
```

### 方法2: 直接使用补丁脚本
```bash
# 检查是否需要修复
./patches/gemini-api-fix.patch check

# 应用修复
./patches/gemini-api-fix.patch apply

# 验证结果
./patches/gemini-api-fix.patch verify
```

### 方法3: 集成到更新流程
```bash
# 完整更新（包含Gemini补丁）
./sh/update.sh update

# 仅应用API端点补丁
./sh/update.sh api-patch
```

### 方法4: 手动修复
```bash
# 编辑文件
vim controller/channel.go

# 找到相关行，将：
# url = fmt.Sprintf("%s/v1beta/openai/models", baseURL)
# 修改为：
# url = fmt.Sprintf("%s/v1beta/models", baseURL)
```

## 验证修复

### 1. 代码验证
```bash
# 检查修复结果
grep -n "v1beta.*models" controller/channel.go

# 应该看到类似输出：
# 182:    url = fmt.Sprintf("%s/v1beta/models", baseURL)
# 778:    url = fmt.Sprintf("%s/v1beta/models", baseURL)

# 确保没有错误的端点
grep -n "v1beta/openai/models" controller/channel.go
# 应该没有输出
```

### 2. 功能验证
1. 重新编译并重启服务
2. 登录管理界面
3. 进入渠道管理页面
4. 添加或编辑 Google Gemini 渠道
5. 点击"获取模型"按钮
6. 应该能正确获取到 Gemini 模型列表

### 3. 前端问题处理
如果后端修复完成但前端仍显示加载状态：

**原因**: 浏览器缓存了旧的错误响应
**解决方案**:
1. 清除浏览器缓存
2. 强制刷新页面 (Ctrl+F5)
3. 尝试无痕模式
4. 检查浏览器开发者工具的 Console 和 Network 标签

## 自动化保护

### 更新流程保护
每次执行 `./sh/update.sh update` 时，系统会自动：
1. 拉取最新代码
2. 应用子路径补丁
3. **应用API端点补丁（包含Gemini修复）**
4. 验证所有配置
5. 重新构建服务

### 备份机制
- 每次应用补丁前都会创建备份
- 备份文件：`controller/channel.go.backup`
- 包含修改前的文件内容

## 技术细节

### 问题根因

#### API 端点错误
Google Gemini API 的官方端点是 `v1beta/models`，而代码中使用的 `v1beta/openai/models` 看起来像是某种 OpenAI 兼容层的路径，但这不是 Google 官方提供的端点。

### 参考文档
- [Google Gemini API 官方文档](https://ai.google.dev/api/models)
- [模型列表端点](https://ai.google.dev/api/models#method:-models.list)
- [官方示例](https://ai.google.dev/api/models#example-request)

### 正确的API调用
```bash
curl https://generativelanguage.googleapis.com/v1beta/models?key=$GEMINI_API_KEY
```

## 维护说明

### 定期检查
建议在每次更新后运行验证：
```bash
./patches/gemini-api-fix.patch verify
```

### 监控更新
如果官方代码再次引入类似问题，补丁系统会自动检测并修复。

### 扩展补丁
如果需要添加其他API端点的修复，可以参考 `patches/gemini-api-fix.patch` 的实现方式。

## 故障排除

### 补丁应用失败
```bash
# 检查文件是否存在
ls -la controller/channel.go

# 检查文件权限
chmod 644 controller/channel.go

# 手动应用修复
sed -i 's|v1beta/openai/models|v1beta/models|g' controller/channel.go
```

### 验证失败
```bash
# 查看当前配置
grep -A5 -B5 "ChannelTypeGemini" controller/channel.go

# 重新应用补丁
./patches/gemini-api-fix.patch apply
```

## 总结

通过这个简化的补丁系统，我们确保了：
1. **问题得到根本解决**：修复了错误的API端点
2. **更新安全**：每次更新都会自动应用修复
3. **操作简便**：提供多种修复方式
4. **文档完整**：详细的问题说明和解决方案
5. **维护简单**：简化的补丁系统，专注核心问题

现在你可以放心地从官方仓库拉取更新，系统会自动保护你的自定义修改！

## 当前状态

✅ **Gemini API 端点已修复**
- 当前使用正确的端点：`v1beta/models`
- 服务已重新构建并运行
- 补丁系统已简化，专注于端点修复功能
